import os
import datetime
import threading
import time
import requests
from flask import Flask, render_template_string, request, redirect, url_for, flash
from dotenv import load_dotenv

load_dotenv()

app = Flask(__name__)
app.secret_key = os.environ.get("FLASK_SECRET_KEY", "supersecret")
SOURCEREST_API_KEY = os.environ.get('SOURCEREST_API_KEY')

buses = []
alerts_log = []

FORM_HTML = """
<!DOCTYPE html>
<html>
<head>
    <title>RC & FC Alert System</title>
</head>
<body>
    <h2>Register Bus</h2>
    {% with messages = get_flashed_messages() %}
      {% if messages %}
        <ul style="color: green;">
          {% for message in messages %}
            <li>{{ message }}</li>
          {% endfor %}
        </ul>
      {% endif %}
    {% endwith %}
    <form method="post">
        <label>Bus Number:</label><input name="bus_number" required><br>
        <label>RC Expiry Date:</label><input type="date" name="rc_expiry" required><br>
        <label>FC Expiry Date:</label><input type="date" name="fc_expiry" required><br>
        <label>Driver Name:</label><input name="driver" required><br>
        <label>Driver Phone (+countrycode):</label><input name="driver_phone" required><br>
        <button type="submit">Register</button>
    </form>
    <hr>
    <h2>All Buses</h2>
    <table border="1">
        <tr><th>Bus</th><th>RC Expiry</th><th>FC Expiry</th><th>Driver</th><th>Phone</th></tr>
        {% for bus in buses %}
        <tr>
            <td>{{bus['bus_number']}}</td>
            <td>{{bus['rc_expiry']}}</td>
            <td>{{bus['fc_expiry']}}</td>
            <td>{{bus['driver']}}</td>
            <td>{{bus['driver_phone']}}</td>
        </tr>
        {% endfor %}
    </table>
    <hr>
    <h2>Alerts Log</h2>
    <ul>
    {% for log in alerts_log %}
        <li>{{log}}</li>
    {% endfor %}
    </ul>
    <hr>
    <h2>Example: Call Gemini AI API with curl</h2>
    <pre>
curl "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=GEMINI_API_KEY" \
  -H 'Content-Type: application/json' \
  -X POST \
  -d '{
    "contents": [
      {
        "parts": [
          {
            "text": "Explain how AI works in a few words"
          }
        ]
      }
    ]
  }'
    </pre>
</body>
</html>
"""

def send_sms(to_number, message):
    if not SOURCEREST_API_KEY:
        print("Sourcerest API key not set. Skipping SMS.")
        return
    try:
        response = requests.post(
            "https://api.sourcerest.com/sms/send",
            headers={
                "Authorization": f"Bearer {SOURCEREST_API_KEY}",
                "Content-Type": "application/json"
            },
            json={
                "to": to_number,
                "message": message
            }
        )
        if response.status_code == 200:
            print(f"SMS sent to {to_number}")
        else:
            print(f"SMS send failed: {response.text}")
    except Exception as e:
        print(f"SMS send failed: {e}")

def check_and_alert():
    while True:
        today = datetime.date.today()
        for bus in buses:
            for doc_type in ['rc', 'fc']:
                expiry_str = bus.get(f"{doc_type}_expiry")
                if not expiry_str:
                    continue
                try:
                    expiry = datetime.datetime.strptime(expiry_str, "%Y-%m-%d").date()
                except ValueError:
                    continue
                days_left = (expiry - today).days
                for interval in [30, 15, 7, 1, 0]:
                    if days_left == interval:
                        message = f"{doc_type.upper()} for Bus {bus['bus_number']} expires on {expiry}.\nPlease renew promptly."
                        send_sms(bus['driver_phone'], message)
                        log_entry = f"{datetime.datetime.now()}: SMS alert sent to {bus['driver_phone']} for Bus {bus['bus_number']} ({doc_type.upper()}) - {days_left} day(s) left"
                        if log_entry not in alerts_log:
                            alerts_log.append(log_entry)
        time.sleep(60)

@app.route('/', methods=['GET', 'POST'])
def index():
    if request.method == 'POST':
        bus = {
            'bus_number': request.form['bus_number'],
            'rc_expiry': request.form['rc_expiry'],
            'fc_expiry': request.form['fc_expiry'],
            'driver': request.form['driver'],
            'driver_phone': request.form['driver_phone']
        }
        buses.append(bus)
        flash("Bus registered successfully!")
        return redirect(url_for('index'))
    return render_template_string(FORM_HTML, buses=buses, alerts_log=alerts_log)

if __name__ == '__main__':
    threading.Thread(target=check_and_alert, daemon=True).start()
    app.run(debug=False, host='0.0.0.0', port=